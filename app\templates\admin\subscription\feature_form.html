{% extends "admin/admin_layout.html" %}

{% block title %}{{ action }} Feature - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-{% if action == 'Create' %}plus{% else %}edit{% endif %} me-2"></i>
                    {{ action }} Feature
                </h2>
                <a href="{{ url_for('admin.subscription_features') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Features
                </a>
            </div>

            <form method="POST">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Feature Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Feature Name (Internal) *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ feature.name if feature else '' }}" required>
                                <div class="form-text">Used internally, must be unique (e.g., posts_per_month)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="display_name" class="form-label">Display Name *</label>
                                <input type="text" class="form-control" id="display_name" name="display_name" 
                                       value="{{ feature.display_name if feature else '' }}" required>
                                <div class="form-text">Shown to users (e.g., Posts per Month)</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Describe what this feature does...">{{ feature.description if feature else '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="feature_type" class="form-label">Feature Type *</label>
                                <select class="form-select" id="feature_type" name="feature_type" required>
                                    <option value="limit" {% if feature and feature.feature_type.value == 'limit' %}selected{% endif %}>
                                        Limit (Numeric limit)
                                    </option>
                                    <option value="boolean" {% if feature and feature.feature_type.value == 'boolean' %}selected{% endif %}>
                                        Boolean (True/False)
                                    </option>
                                    <option value="quota" {% if feature and feature.feature_type.value == 'quota' %}selected{% endif %}>
                                        Quota (Usage-based)
                                    </option>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>Limit:</strong> Maximum allowed (e.g., 100 posts)<br>
                                        <strong>Boolean:</strong> Feature enabled/disabled<br>
                                        <strong>Quota:</strong> Usage tracked over time
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="default_value" class="form-label">Default Value *</label>
                                <input type="text" class="form-control" id="default_value" name="default_value" 
                                       value="{{ feature.default_value if feature else '0' }}" required>
                                <div class="form-text">Default value for new plans</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="unit" class="form-label">Unit</label>
                                <input type="text" class="form-control" id="unit" name="unit" 
                                       value="{{ feature.unit if feature else '' }}" 
                                       placeholder="e.g., posts, users, GB">
                                <div class="form-text">Optional unit of measurement</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Feature Type Examples</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Limit Features:</strong>
                                            <ul class="mb-0">
                                                <li>Posts per month: 100</li>
                                                <li>Social accounts: 5</li>
                                                <li>Team members: 3</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Boolean Features:</strong>
                                            <ul class="mb-0">
                                                <li>Priority support: true</li>
                                                <li>Analytics access: false</li>
                                                <li>White label: true</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Quota Features:</strong>
                                            <ul class="mb-0">
                                                <li>API calls: 10000</li>
                                                <li>Storage space: 1000</li>
                                                <li>Bandwidth: 100</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.subscription_features') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ action }} Feature
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const featureTypeSelect = document.getElementById('feature_type');
    const defaultValueInput = document.getElementById('default_value');
    
    function updateDefaultValuePlaceholder() {
        const selectedType = featureTypeSelect.value;
        
        switch(selectedType) {
            case 'limit':
                defaultValueInput.placeholder = 'e.g., 100';
                break;
            case 'boolean':
                defaultValueInput.placeholder = 'true or false';
                break;
            case 'quota':
                defaultValueInput.placeholder = 'e.g., 1000';
                break;
        }
    }
    
    featureTypeSelect.addEventListener('change', updateDefaultValuePlaceholder);
    updateDefaultValuePlaceholder(); // Set initial placeholder
});
</script>
{% endblock %}
