{% extends "admin/admin_layout.html" %}

{% block title %}User Subscriptions - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-users me-2"></i>
                    User Subscriptions
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.subscription_analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-2"></i>Analytics
                    </a>
                    <a href="{{ url_for('admin.subscription_plans') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>Manage Plans
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Subscriptions</h6>
                                    <h3 class="mb-0">{{ subscriptions|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active</h6>
                                    <h3 class="mb-0">{{ subscriptions|selectattr('status.value', 'equalto', 'active')|list|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Trialing</h6>
                                    <h3 class="mb-0">{{ subscriptions|selectattr('status.value', 'equalto', 'trialing')|list|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Canceled</h6>
                                    <h3 class="mb-0">{{ subscriptions|selectattr('status.value', 'equalto', 'canceled')|list|length }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">All Subscriptions</h5>
                        </div>
                        <div class="col-auto">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search users...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if subscriptions %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="subscriptionsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Status</th>
                                    <th>Billing</th>
                                    <th>Amount</th>
                                    <th>Start Date</th>
                                    <th>Next Billing</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in subscriptions %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ subscription.user.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ subscription.user.email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ subscription.plan_name }}</span>
                                        {% if subscription.custom_plan %}
                                        <br><small class="text-info">Custom Plan</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if subscription.status.value == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                        {% elif subscription.status.value == 'trialing' %}
                                        <span class="badge bg-info">Trialing</span>
                                        {% elif subscription.status.value == 'canceled' %}
                                        <span class="badge bg-danger">Canceled</span>
                                        {% elif subscription.status.value == 'expired' %}
                                        <span class="badge bg-warning">Expired</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ subscription.status.value.title() }}</span>
                                        {% endif %}
                                        
                                        {% if subscription.is_trial %}
                                        <br><small class="text-info">Trial until {{ subscription.trial_end_date.strftime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-outline-primary">{{ subscription.billing_cycle.value.title() }}</span>
                                    </td>
                                    <td>
                                        <strong>${{ "%.2f"|format(subscription.amount) }}</strong>
                                        <br>
                                        <small class="text-muted">{{ subscription.currency }}</small>
                                    </td>
                                    <td>
                                        {{ subscription.start_date.strftime('%Y-%m-%d') }}
                                    </td>
                                    <td>
                                        {% if subscription.next_billing_date %}
                                        {{ subscription.next_billing_date.strftime('%Y-%m-%d') }}
                                        <br>
                                        <small class="text-muted">
                                            {% set days = subscription.days_until_renewal %}
                                            {% if days == 0 %}
                                            Today
                                            {% elif days == 1 %}
                                            Tomorrow
                                            {% else %}
                                            {{ days }} days
                                            {% endif %}
                                        </small>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewSubscription('{{ subscription.id }}')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if subscription.status.value in ['active', 'trialing'] %}
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="cancelSubscription('{{ subscription.id }}')" title="Cancel">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No subscriptions found</h5>
                        <p class="text-muted">Users will appear here once they subscribe to plans.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Details Modal -->
<div class="modal fade" id="subscriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Subscription Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="subscriptionDetails">
                    <p class="text-center">Loading...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#subscriptionsTable tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

function viewSubscription(subscriptionId) {
    // In a full implementation, this would fetch subscription details via AJAX
    document.getElementById('subscriptionDetails').innerHTML = `
        <p>Subscription ID: ${subscriptionId}</p>
        <p>Detailed subscription information would be loaded here...</p>
    `;
    new bootstrap.Modal(document.getElementById('subscriptionModal')).show();
}

function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription?')) {
        // In a full implementation, this would make an AJAX call
        alert('Subscription cancellation would be processed here');
    }
}
</script>
{% endblock %}
