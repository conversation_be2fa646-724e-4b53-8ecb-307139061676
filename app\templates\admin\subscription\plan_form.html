{% extends "admin/admin_layout.html" %}

{% block title %}{{ action }} Subscription Plan - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-{% if action == 'Create' %}plus{% else %}edit{% endif %} me-2"></i>
                    {{ action }} Subscription Plan
                </h2>
                <a href="{{ url_for('admin.subscription_plans') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Plans
                </a>
            </div>

            <form method="POST">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Plan Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Plan Name (Internal) *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ plan.name if plan else '' }}" required>
                                        <div class="form-text">Used internally, must be unique</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="display_name" class="form-label">Display Name *</label>
                                        <input type="text" class="form-control" id="display_name" name="display_name" 
                                               value="{{ plan.display_name if plan else '' }}" required>
                                        <div class="form-text">Shown to users</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3">{{ plan.description if plan else '' }}</textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="visibility" class="form-label">Visibility</label>
                                        <select class="form-select" id="visibility" name="visibility">
                                            <option value="public" {% if plan and plan.visibility.value == 'public' %}selected{% endif %}>Public</option>
                                            <option value="private" {% if plan and plan.visibility.value == 'private' %}selected{% endif %}>Private</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                   {% if not plan or plan.is_active %}checked{% endif %}>
                                            <label class="form-check-label" for="is_active">
                                                Active Plan
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Pricing</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="monthly_price" class="form-label">Monthly Price ($)</label>
                                        <input type="number" class="form-control" id="monthly_price" name="monthly_price" 
                                               step="0.01" min="0" 
                                               value="{% if plan %}{% for tier in plan.pricing_tiers %}{% if tier.billing_cycle.value == 'monthly' %}{{ tier.price }}{% endif %}{% endfor %}{% endif %}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="yearly_price" class="form-label">Yearly Price ($)</label>
                                        <input type="number" class="form-control" id="yearly_price" name="yearly_price" 
                                               step="0.01" min="0"
                                               value="{% if plan %}{% for tier in plan.pricing_tiers %}{% if tier.billing_cycle.value == 'yearly' %}{{ tier.price }}{% endif %}{% endfor %}{% endif %}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="trial_days" class="form-label">Trial Days</label>
                                        <input type="number" class="form-control" id="trial_days" name="trial_days" 
                                               min="0" value="{% if plan and plan.pricing_tiers %}{{ plan.pricing_tiers[0].trial_days }}{% else %}0{% endif %}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Features</h5>
                            </div>
                            <div class="card-body">
                                {% if features %}
                                <div class="row">
                                    {% for feature in features %}
                                    <div class="col-md-6 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-3">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="feature_{{ feature.id }}" name="features" value="{{ feature.id }}"
                                                           {% if plan %}
                                                           {% for plan_feature in plan.features %}
                                                           {% if plan_feature.feature.id == feature.id %}checked{% endif %}
                                                           {% endfor %}
                                                           {% endif %}>
                                                    <label class="form-check-label fw-bold" for="feature_{{ feature.id }}">
                                                        {{ feature.display_name }}
                                                    </label>
                                                </div>
                                                <p class="text-muted small mb-2">{{ feature.description }}</p>
                                                
                                                <div class="row">
                                                    <div class="col-8">
                                                        <input type="text" class="form-control form-control-sm" 
                                                               name="feature_value_{{ feature.id }}" 
                                                               placeholder="Value{% if feature.unit %} ({{ feature.unit }}){% endif %}"
                                                               {% if plan %}
                                                               {% for plan_feature in plan.features %}
                                                               {% if plan_feature.feature.id == feature.id and not plan_feature.is_unlimited %}
                                                               value="{{ plan_feature.value }}"
                                                               {% endif %}
                                                               {% endfor %}
                                                               {% endif %}>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="feature_unlimited_{{ feature.id }}"
                                                                   {% if plan %}
                                                                   {% for plan_feature in plan.features %}
                                                                   {% if plan_feature.feature.id == feature.id and plan_feature.is_unlimited %}checked{% endif %}
                                                                   {% endfor %}
                                                                   {% endif %}>
                                                            <label class="form-check-label small">
                                                                Unlimited
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="text-center py-3">
                                    <p class="text-muted">No features available. <a href="{{ url_for('admin.subscription_features') }}">Create features first</a>.</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card shadow-sm position-sticky" style="top: 20px;">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Actions</h5>
                            </div>
                            <div class="card-body">
                                <button type="submit" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-save me-2"></i>{{ action }} Plan
                                </button>
                                <a href="{{ url_for('admin.subscription_plans') }}" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle feature value input when unlimited is checked
document.addEventListener('DOMContentLoaded', function() {
    const unlimitedCheckboxes = document.querySelectorAll('input[name^="feature_unlimited_"]');
    
    unlimitedCheckboxes.forEach(checkbox => {
        const featureId = checkbox.name.replace('feature_unlimited_', '');
        const valueInput = document.querySelector(`input[name="feature_value_${featureId}"]`);
        
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                valueInput.disabled = true;
                valueInput.value = '';
            } else {
                valueInput.disabled = false;
            }
        });
        
        // Set initial state
        if (checkbox.checked) {
            valueInput.disabled = true;
        }
    });
});
</script>
{% endblock %}
