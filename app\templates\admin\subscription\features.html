{% extends "admin/admin_layout.html" %}

{% block title %}Subscription Features - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-cogs me-2"></i>
                    Subscription Features
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.subscription_feature_create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Feature
                    </a>
                    <a href="{{ url_for('admin.subscription_plans') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>Back to Plans
                    </a>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    {% if features %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Feature Name</th>
                                    <th>Display Name</th>
                                    <th>Type</th>
                                    <th>Default Value</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for feature in features %}
                                <tr>
                                    <td>
                                        <strong>{{ feature.name }}</strong>
                                    </td>
                                    <td>{{ feature.display_name }}</td>
                                    <td>
                                        {% if feature.feature_type.value == 'limit' %}
                                        <span class="badge bg-info">Limit</span>
                                        {% elif feature.feature_type.value == 'boolean' %}
                                        <span class="badge bg-success">Boolean</span>
                                        {% else %}
                                        <span class="badge bg-warning">Quota</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ feature.default_value }}</code>
                                    </td>
                                    <td>
                                        {% if feature.unit %}
                                        <span class="badge bg-secondary">{{ feature.unit }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if feature.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editFeature('{{ feature.id }}')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="toggleFeature('{{ feature.id }}', {{ feature.is_active|lower }})" 
                                                    title="{% if feature.is_active %}Deactivate{% else %}Activate{% endif %}">
                                                <i class="fas fa-{% if feature.is_active %}pause{% else %}play{% endif %}"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No features found</h5>
                        <p class="text-muted">Create your first feature to get started.</p>
                        <a href="{{ url_for('admin.subscription_feature_create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create First Feature
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feature Info Modal -->
<div class="modal fade" id="featureModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Feature Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Feature Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td id="modalFeatureName"></td>
                            </tr>
                            <tr>
                                <td><strong>Display Name:</strong></td>
                                <td id="modalFeatureDisplayName"></td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td id="modalFeatureType"></td>
                            </tr>
                            <tr>
                                <td><strong>Default Value:</strong></td>
                                <td id="modalFeatureDefaultValue"></td>
                            </tr>
                            <tr>
                                <td><strong>Unit:</strong></td>
                                <td id="modalFeatureUnit"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Description</h6>
                        <p id="modalFeatureDescription" class="text-muted"></p>
                        
                        <h6>Usage in Plans</h6>
                        <div id="modalFeatureUsage">
                            <p class="text-muted">Loading...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editFeature(featureId) {
    // For now, just show feature details
    // In a full implementation, this would open an edit form
    showFeatureDetails(featureId);
}

function showFeatureDetails(featureId) {
    // Find feature data from the table
    const row = document.querySelector(`button[onclick="editFeature('${featureId}')"]`).closest('tr');
    const cells = row.querySelectorAll('td');
    
    document.getElementById('modalFeatureName').textContent = cells[0].querySelector('strong').textContent;
    document.getElementById('modalFeatureDisplayName').textContent = cells[1].textContent;
    document.getElementById('modalFeatureType').innerHTML = cells[2].innerHTML;
    document.getElementById('modalFeatureDefaultValue').innerHTML = cells[3].innerHTML;
    document.getElementById('modalFeatureUnit').innerHTML = cells[4].innerHTML;
    
    // Show modal
    new bootstrap.Modal(document.getElementById('featureModal')).show();
}

function toggleFeature(featureId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to ${action} this feature?`)) {
        // In a full implementation, this would make an AJAX call
        // For now, just reload the page
        location.reload();
    }
}
</script>
{% endblock %}
